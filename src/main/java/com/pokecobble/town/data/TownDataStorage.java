package com.pokecobble.town.data;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.TownPlayer;
import com.pokecobble.town.TownPlayerRank;
import net.fabricmc.loader.api.FabricLoader;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import com.pokecobble.town.TownPlayerRank;

/**
 * Handles saving and loading town data to/from disk.
 * Each town is saved to its own individual file in the towns/ subdirectory.
 */
public class TownDataStorage {
    // Constants for file paths
    private static final String DATA_FOLDER = "pokecobbleclaim";
    private static final String TOWNS_FOLDER = "towns";
    private static final String TOWNS_FILE = "towns.json"; // Legacy file for migration
    private static final String TOWN_FILE_EXTENSION = ".json";

    // GSON instance for serialization
    private static final Gson GSON = new GsonBuilder().setPrettyPrinting().create();

    /**
     * Saves all towns to individual files.
     */
    public static void saveTowns() {
        try {
            // Get all towns from the TownManager
            Collection<Town> towns = TownManager.getInstance().getAllTowns();

            // Create towns directory if it doesn't exist
            File townsDir = getTownsDirectory();
            if (!townsDir.exists()) {
                townsDir.mkdirs();
            }

            int savedCount = 0;
            for (Town town : towns) {
                try {
                    saveTown(town);
                    savedCount++;
                } catch (Exception e) {
                    Pokecobbleclaim.LOGGER.error("Failed to save individual town '" + town.getName() + "': " + e.getMessage());
                }
            }

            Pokecobbleclaim.LOGGER.info("Saved " + savedCount + " towns to individual files");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to save towns: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Loads all towns from individual files.
     */
    public static void loadTowns() {
        // Use the new individual file loading method
        loadTownsFromIndividualFiles();

        // Log final state after loading
        int finalTownCount = TownManager.getInstance().getAllTowns().size();
        Pokecobbleclaim.LOGGER.info("Town data loading completed. Final town count in TownManager: " + finalTownCount);
    }

    /**
     * Saves a single town to its own file.
     *
     * @param town The town to save
     */
    public static void saveTown(Town town) {
        try {
            // Create towns directory if it doesn't exist
            File townsDir = getTownsDirectory();
            if (!townsDir.exists()) {
                townsDir.mkdirs();
            }

            // Convert town to serializable format
            SerializableTown serializableTown = new SerializableTown(town);

            // Create town file
            File townFile = new File(townsDir, town.getId().toString() + TOWN_FILE_EXTENSION);

            // Use atomic file operations to prevent data corruption
            File tempFile = File.createTempFile("town_", ".tmp", townsDir);

            try (FileWriter writer = new FileWriter(tempFile)) {
                GSON.toJson(serializableTown, writer);
            }

            // Atomic move from temp file to final file
            if (!tempFile.renameTo(townFile)) {
                // If rename fails, try copy and delete
                try (FileReader reader = new FileReader(tempFile);
                     FileWriter writer = new FileWriter(townFile)) {
                    char[] buffer = new char[8192];
                    int length;
                    while ((length = reader.read(buffer)) != -1) {
                        writer.write(buffer, 0, length);
                    }
                }
                tempFile.delete();
            }

            Pokecobbleclaim.LOGGER.info("Saved town '" + town.getName() + "' to individual file: " + townFile.getName());
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to save town '" + town.getName() + "': " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Loads a single town from its file.
     *
     * @param townId The UUID of the town to load
     * @return The loaded town, or null if not found
     */
    public static Town loadTown(UUID townId) {
        try {
            File townsDir = getTownsDirectory();
            if (!townsDir.exists()) {
                return null;
            }

            File townFile = new File(townsDir, townId.toString() + TOWN_FILE_EXTENSION);
            if (!townFile.exists()) {
                return null;
            }

            try (FileReader reader = new FileReader(townFile)) {
                SerializableTown serializableTown = GSON.fromJson(reader, SerializableTown.class);
                if (serializableTown != null) {
                    Town town = serializableTown.toTown();
                    Pokecobbleclaim.LOGGER.debug("Loaded town '" + town.getName() + "' from individual file");
                    return town;
                }
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to load town " + townId + ": " + e.getMessage());
            e.printStackTrace();
        }
        return null;
    }

    /**
     * Loads all towns from individual files.
     */
    public static void loadTownsFromIndividualFiles() {
        try {
            File townsDir = getTownsDirectory();
            if (!townsDir.exists()) {
                Pokecobbleclaim.LOGGER.info("No towns directory found, creating new directory");
                townsDir.mkdirs();
                return;
            }

            File[] townFiles = townsDir.listFiles((dir, name) -> name.endsWith(TOWN_FILE_EXTENSION));
            if (townFiles == null || townFiles.length == 0) {
                Pokecobbleclaim.LOGGER.info("No individual town files found");
                return;
            }

            Pokecobbleclaim.LOGGER.info("Found " + townFiles.length + " individual town files, loading...");

            // Clear existing towns but keep player-town mappings
            TownManager.getInstance().clearTownsKeepMappings();

            int totalPlayersRestored = 0;
            int loadedTowns = 0;

            for (File townFile : townFiles) {
                try (FileReader reader = new FileReader(townFile)) {
                    SerializableTown serializableTown = GSON.fromJson(reader, SerializableTown.class);
                    if (serializableTown != null) {
                        Town town = serializableTown.toTown();

                        // Add town without immediate synchronization during loading
                        TownManager.getInstance().addTown(town, false);

                        // Update player-town mappings for all players in this town
                        for (UUID playerId : town.getPlayers()) {
                            TownManager.getInstance().updatePlayerTownMapping(playerId, town.getId());
                            totalPlayersRestored++;
                        }

                        // Mark town as having changed player and rank data to ensure synchronization after server restart
                        town.markChanged(Town.ASPECT_PLAYERS);
                        town.markChanged(Town.ASPECT_RANKS);
                        town.markChanged(Town.ASPECT_PERMISSIONS);

                        loadedTowns++;
                        Pokecobbleclaim.LOGGER.info("Loaded town '" + town.getName() + "' with " + town.getPlayerCount() + " players from " + townFile.getName());
                    }
                } catch (Exception e) {
                    Pokecobbleclaim.LOGGER.error("Failed to load town from file " + townFile.getName() + ": " + e.getMessage());
                    e.printStackTrace();
                }
            }

            Pokecobbleclaim.LOGGER.info("Restored " + totalPlayersRestored + " player-town relationships");
            Pokecobbleclaim.LOGGER.info("Loaded " + loadedTowns + " towns from individual files");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to load towns from individual files: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Deletes a town's individual file.
     *
     * @param townId The UUID of the town to delete
     * @return true if the file was deleted successfully, false otherwise
     */
    public static boolean deleteTown(UUID townId) {
        try {
            File townsDir = getTownsDirectory();
            if (!townsDir.exists()) {
                return false;
            }

            File townFile = new File(townsDir, townId.toString() + TOWN_FILE_EXTENSION);
            if (townFile.exists()) {
                boolean deleted = townFile.delete();
                if (deleted) {
                    Pokecobbleclaim.LOGGER.info("Deleted town file: " + townFile.getName());
                } else {
                    Pokecobbleclaim.LOGGER.warn("Failed to delete town file: " + townFile.getName());
                }
                return deleted;
            }
            return true; // File doesn't exist, consider it "deleted"
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to delete town file for " + townId + ": " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * Gets the towns directory, creating it if it doesn't exist.
     *
     * @return The towns directory
     */
    private static File getTownsDirectory() {
        File dataDir = new File(FabricLoader.getInstance().getGameDir().toFile(), DATA_FOLDER);
        if (!dataDir.exists()) {
            dataDir.mkdirs();
        }
        return new File(dataDir, TOWNS_FOLDER);
    }

    /**
     * A serializable version of the Town class.
     */
    private static class SerializableTown {
        private String name;
        private List<String> playerIds;
        private Map<String, String> playerNames; // Map of UUID -> player name for persistence
        private String id;
        private String description;
        private boolean isOpen;
        private int maxPlayers;
        private String mayorId;
        private boolean inElection;
        private int claimCount;
        private Map<String, Map<String, Boolean>> permissions;
        private Map<String, String> playerRanks;
        private String image; // Town image name
        private long creationDate; // Creation date as timestamp
        private Map<String, Object> townSettings; // Town settings from TownSettingsManager

        public SerializableTown() {
            // Default constructor for GSON
        }

        public SerializableTown(Town town) {
            this.name = town.getName();
            this.playerIds = new ArrayList<>();
            this.playerNames = new HashMap<>();

            // Save both player IDs and names for better persistence
            for (UUID playerId : town.getPlayers()) {
                String playerIdStr = playerId.toString();
                this.playerIds.add(playerIdStr);

                // Get player name from TownPlayer object
                TownPlayer townPlayer = town.getPlayer(playerId);
                if (townPlayer != null && townPlayer.getName() != null && !townPlayer.getName().isEmpty()) {
                    this.playerNames.put(playerIdStr, townPlayer.getName());
                } else {
                    // Fallback: try to get name from server if available
                    this.playerNames.put(playerIdStr, "Unknown Player");
                }
            }

            this.id = town.getId().toString();
            this.description = town.getDescription();
            this.isOpen = town.isOpen();
            this.maxPlayers = town.getMaxPlayers();

            // Save mayor ID (first player in the list is the mayor/owner)
            if (!town.getPlayers().isEmpty()) {
                UUID mayorId = town.getPlayers().get(0);
                this.mayorId = mayorId.toString();
            }

            this.inElection = town.isInElection();
            this.claimCount = town.getClaimCount();

            // Save permissions (town doesn't have permissions directly, only players do)
            this.permissions = new HashMap<>();

            // Save player ranks
            this.playerRanks = new HashMap<>();
            for (UUID playerId : town.getPlayers()) {
                TownPlayerRank rank = town.getPlayerRank(playerId);
                if (rank != null) {
                    this.playerRanks.put(playerId.toString(), rank.name());
                }
            }

            // Save town image
            this.image = town.getImage();

            // Save creation date
            if (town.getCreationDate() != null) {
                this.creationDate = town.getCreationDate().getTime();
            } else {
                // Fallback for existing towns without creation date
                this.creationDate = System.currentTimeMillis();
            }

            // Save town settings from TownSettingsManager
            try {
                this.townSettings = com.pokecobble.town.config.TownSettingsManager.getTownSettings(town.getId());
                Pokecobbleclaim.LOGGER.debug("Saved town settings for town " + town.getName() + ": " + this.townSettings);
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.warn("Failed to save town settings for town " + town.getName() + ": " + e.getMessage());
                this.townSettings = new HashMap<>();
            }
        }

        public Town toTown() {
            Town town = new Town(name);
            // Set ID
            try {
                UUID townId = UUID.fromString(id);
                town.setId(townId);
            } catch (IllegalArgumentException e) {
                Pokecobbleclaim.LOGGER.warn("Invalid town ID: " + id + ", generating new ID");
            }

            // Set other properties
            town.setDescription(description);
            town.setOpen(isOpen);
            town.setMaxPlayers(maxPlayers);

            // Add players with proper TownPlayer objects
            for (String playerIdStr : playerIds) {
                try {
                    UUID playerId = UUID.fromString(playerIdStr);

                    // Get player name - prioritize stored name, fallback to PlayerDataStorage
                    String playerName = "Unknown Player";
                    if (playerNames != null && playerNames.containsKey(playerIdStr)) {
                        playerName = playerNames.get(playerIdStr);
                    } else {
                        // Fallback: try to load player data from disk to get the name
                        try {
                            PlayerDataStorage.SerializablePlayerData playerData =
                                PlayerDataStorage.loadPlayerData(playerId);
                            if (playerData != null && playerData.getPlayerName() != null && !playerData.getPlayerName().isEmpty()) {
                                playerName = playerData.getPlayerName();
                            }
                        } catch (Exception e) {
                            Pokecobbleclaim.LOGGER.debug("Could not load player data for " + playerId + ", using default name");
                        }
                    }

                    // Get player rank from saved data
                    TownPlayerRank playerRank = TownPlayerRank.MEMBER;
                    if (playerRanks != null && playerRanks.containsKey(playerIdStr)) {
                        try {
                            playerRank = TownPlayerRank.valueOf(playerRanks.get(playerIdStr));

                            // Data migration: Fix any VISITOR ranks for town members
                            if (playerRank == TownPlayerRank.VISITOR) {
                                Pokecobbleclaim.LOGGER.warn("Found VISITOR rank for town member " + playerIdStr + " in town " + name + ", correcting to MEMBER");
                                playerRank = TownPlayerRank.MEMBER;
                                // Update the saved data to fix this permanently
                                playerRanks.put(playerIdStr, playerRank.name());
                            }
                        } catch (IllegalArgumentException e) {
                            Pokecobbleclaim.LOGGER.warn("Invalid player rank for " + playerIdStr + ": " + playerRanks.get(playerIdStr));
                        }
                    }

                    // Create TownPlayer object with proper data
                    TownPlayer townPlayer = new TownPlayer(playerId, playerName, playerRank);
                    townPlayer.setOnline(false); // Player is offline during server startup

                    // Add the TownPlayer to the town using the proper method
                    town.addPlayer(townPlayer);

                    // Also set the player rank explicitly to ensure it's properly stored
                    town.setPlayerRank(playerId, playerRank);

                    // Update the player-town mapping in TownManager
                    TownManager.getInstance().updatePlayerTownMapping(playerId, town.getId());

                    Pokecobbleclaim.LOGGER.debug("Restored player " + playerName + " (" + playerId + ") with rank " + playerRank + " to town " + name);
                } catch (IllegalArgumentException e) {
                    Pokecobbleclaim.LOGGER.warn("Invalid player ID: " + playerIdStr + ", skipping");
                }
            }

            // Set mayor (by setting player rank to OWNER)
            if (mayorId != null && !town.getPlayers().isEmpty()) {
                try {
                    UUID mayorUuid = UUID.fromString(mayorId);
                    // If the mayor is in the player list, set their rank to OWNER
                    if (town.getPlayers().contains(mayorUuid)) {
                        town.setPlayerRank(mayorUuid, TownPlayerRank.OWNER);
                    }
                } catch (IllegalArgumentException e) {
                    Pokecobbleclaim.LOGGER.warn("Invalid mayor ID: " + mayorId + ", skipping");
                }
            }

            // Set election status
            town.setInElection(inElection);

            // Set claim count
            town.setClaimCount(claimCount);

            // Set permissions (town doesn't have permissions directly, only players do)
            // We'll skip this for now

            // Set player ranks
            if (playerRanks != null) {
                for (Map.Entry<String, String> entry : playerRanks.entrySet()) {
                    try {
                        UUID playerId = UUID.fromString(entry.getKey());
                        TownPlayerRank rank = TownPlayerRank.valueOf(entry.getValue());
                        town.setPlayerRank(playerId, rank);
                    } catch (IllegalArgumentException e) {
                        Pokecobbleclaim.LOGGER.warn("Invalid player ID or rank: " + entry.getKey() + " -> " + entry.getValue());
                    }
                }
            }

            // Set town image
            if (image != null) {
                town.setImage(image);
            }

            // Set creation date
            if (creationDate > 0) {
                town.setCreationDate(new java.util.Date(creationDate));
            } else {
                // Fallback for existing towns without creation date
                town.setCreationDate(new java.util.Date(System.currentTimeMillis()));
            }

            // Restore town settings to TownSettingsManager
            if (townSettings != null && !townSettings.isEmpty()) {
                try {
                    com.pokecobble.town.config.TownSettingsManager.setTownSettings(town.getId(), townSettings);
                    Pokecobbleclaim.LOGGER.debug("Restored town settings for town " + town.getName() + ": " + townSettings);
                } catch (Exception e) {
                    Pokecobbleclaim.LOGGER.warn("Failed to restore town settings for town " + town.getName() + ": " + e.getMessage());
                }
            } else {
                Pokecobbleclaim.LOGGER.debug("No saved town settings found for town " + town.getName() + ", will use defaults");
            }

            return town;
        }
    }
}
