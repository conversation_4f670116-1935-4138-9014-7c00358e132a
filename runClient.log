
> Configure project :
Fabric Loom: 1.10.5

> Task :compileJava FAILED
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/town/command/TownClientCommand.java:142: error: package com.pokecobble.town.test does not exist
                    com.pokecobble.town.test.TownSettingsTest.testTownSettings();
                                            ^
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/town/client/ClaimToolDataSynchronizer.java:80: warning: [unchecked] unchecked cast
                Map<String, Object> eventData = (Map<String, Object>) data;
                                                                      ^
  required: Map<String,Object>
  found:    Object
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/config/ConfigSynchronizer.java:148: warning: [unchecked] unchecked cast
            return (Map<String, Object>) categoryPrefs;
                                         ^
  required: Map<String,Object>
  found:    Object
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/config/ConfigSynchronizer.java:192: warning: [unchecked] unchecked conversion
            Map<String, Object> preferences = gson.fromJson(jsonObject, Map.class);
                                                           ^
  required: Map<String,Object>
  found:    Map
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/config/ConfigSynchronizer.java:325: warning: [unchecked] unchecked conversion
            Map<String, Object> config = gson.fromJson(jsonValue, Map.class);
                                                      ^
  required: Map<String,Object>
  found:    Map
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/config/UserPreferencesManager.java:60: warning: [unchecked] unchecked conversion
            Map<String, Map<String, Object>> loaded = gson.fromJson(reader, Map.class);
                                                                   ^
  required: Map<String,Map<String,Object>>
  found:    Map
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/config/UserPreferencesManager.java:192: warning: [unchecked] unchecked cast
            return (T) value;
                       ^
  required: T
  found:    Object
  where T is a type-variable:
    T extends Object declared in method <T>getPreference(String,String,T)
1 error
6 warnings

[Incubating] Problems report is available at: file:///home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/build/reports/problems/problems-report.html

FAILURE: Build failed with an exception.

* What went wrong:
Execution failed for task ':compileJava'.
> Compilation failed; see the compiler output below.
  /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/town/command/TownClientCommand.java:142: error: package com.pokecobble.town.test does not exist
                      com.pokecobble.town.test.TownSettingsTest.testTownSettings();
                                              ^
  /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/town/client/ClaimToolDataSynchronizer.java:80: warning: [unchecked] unchecked cast
                  Map<String, Object> eventData = (Map<String, Object>) data;
                                                                        ^
    required: Map<String,Object>
    found:    Object
  /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/config/ConfigSynchronizer.java:148: warning: [unchecked] unchecked cast
              return (Map<String, Object>) categoryPrefs;
                                           ^
    required: Map<String,Object>
    found:    Object
  /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/config/ConfigSynchronizer.java:192: warning: [unchecked] unchecked conversion
              Map<String, Object> preferences = gson.fromJson(jsonObject, Map.class);
                                                             ^
    required: Map<String,Object>
    found:    Map
  /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/config/ConfigSynchronizer.java:325: warning: [unchecked] unchecked conversion
              Map<String, Object> config = gson.fromJson(jsonValue, Map.class);
                                                        ^
    required: Map<String,Object>
    found:    Map
  /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/config/UserPreferencesManager.java:60: warning: [unchecked] unchecked conversion
              Map<String, Map<String, Object>> loaded = gson.fromJson(reader, Map.class);
                                                                     ^
    required: Map<String,Map<String,Object>>
    found:    Map
  /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/config/UserPreferencesManager.java:192: warning: [unchecked] unchecked cast
              return (T) value;
                         ^
    required: T
    found:    Object
    where T is a type-variable:
      T extends Object declared in method <T>getPreference(String,String,T)
  1 error
  6 warnings

* Try:
> Check your code and dependencies to fix the compilation error(s)
> Run with --scan to get full insights.

BUILD FAILED in 2s
1 actionable task: 1 executed
